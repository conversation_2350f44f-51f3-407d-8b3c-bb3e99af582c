import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { RegisterFormData, registerSchema } from "@/validations";
import { useRouter } from "expo-router";
import { otcRequest } from "@/api/authorization";
import { useErrorHandling } from "./useErrorHandling";
import { Consent } from "@/types/consents";
import { useState } from "react";
import { ErrorRegisterFlowUserConsents } from "@/errors";

interface Props {
  consents: Consent[];
}
export const useRegister = ({ consents }: Props) => {
  const router = useRouter();
  const { withErrorHandling } = useErrorHandling();
  const [checkedConsents, setCheckedConsents] = useState<Consent[]>([]);
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const handleConsentChange = (consent: Consent) => {
    setCheckedConsents((prevConsents) => {
      if (prevConsents.includes(consent)) {
        return prevConsents.filter((c) => c !== consent);
      } else {
        return [...prevConsents, consent];
      }
    });
  };

  const onSubmit = (data: RegisterFormData) =>
    withErrorHandling(async () => {
      const requiredConsents = consents.filter(
        (consent) => consent.isRequired === true
      );

      const allRequiredConsentsChecked = requiredConsents.every((val) =>
        checkedConsents.includes(val)
      );

      if (!allRequiredConsentsChecked) {
        throw new ErrorRegisterFlowUserConsents();
      }

      await otcRequest({
        email: data.email.toLowerCase().trim(),
        purpose: "registration",
      });

      router.replace({
        pathname: "/email-otc",
        params: {
          email: data.email.toLowerCase().trim(),
          name: data.name.trim(),
          password: data.password,
          consentIds: JSON.stringify(checkedConsents.map((val) => val.id)),
        },
      });
    });

  return {
    control,
    handleSubmit,
    errors,
    onSubmit,
    handleConsentChange,
    checkedConsents,
  };
};
