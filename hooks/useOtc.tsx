import React, { useState } from "react";
import { ToastSuccess, useToast } from "@vs/kit-ui-expo";
import { useTranslation } from "react-i18next";
import { useErrorHandling } from "@/hooks/useErrorHandling";
import { useRouter } from "expo-router";
import { EmailOtcParams } from "@/types/authorization";
import { registerRequest } from "@/api/authorization";

export const useOtc = (registrationData: EmailOtcParams) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { withErrorHandling } = useErrorHandling();
  const { showToast } = useToast();
  const [value, setValue] = useState("");

  const handleOtcVerifire = () =>
    withErrorHandling(async () => {
      const { email, name, password, consentIds } = registrationData;
      const consentIdsArray = JSON.parse(consentIds);

      await registerRequest({
        email,
        name,
        password,
        consentIds: consentIdsArray,
        authorization_code: value,
      });

      showToast(<ToastSuccess title={t("email_otc_modal_successs_message")} />);
      router.replace("/login");
    });

  return {
    value,
    setValue,
    handleOtcVerifire,
    registrationData,
  };
};
