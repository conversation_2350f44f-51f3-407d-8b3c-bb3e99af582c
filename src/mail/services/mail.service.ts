import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class MailService {
  constructor(private readonly mailerService: MailerService) {}

  async sendTestWelcomeEmail(to: string, name: string) {
    await this.mailerService.sendMail({
      to,
      subject: 'Welcome!',
      template: 'welcome',
      context: {
        name,
      },
    });
  }

  async sendOtc(to: string, code: string) {
    await this.mailerService.sendMail({
      to,
      subject: 'Your OTC code',
      template: 'otc',
      context: {
        code,
      },
    });
  }
}
