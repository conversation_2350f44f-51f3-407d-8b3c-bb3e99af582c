import { Body, Controller, Post } from '@nestjs/common';
import { MailService } from '@app/mail/services/mail.service';
import { MAIL_ROUTES } from '@app/constants/routes/mail-routes-names';

@Controller({ version: '1' })
export class MailController {
  constructor(private readonly mailService: MailService) {}

  @Post(MAIL_ROUTES.TEST)
  async sendTestEmail(@Body() body: { email: string; name: string }) {
    const { email, name } = body;
    await this.mailService.sendTestWelcomeEmail(email, name);
    return { message: 'Test email sent successfully' };
  }
}
