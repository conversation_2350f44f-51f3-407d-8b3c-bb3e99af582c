export type ErrorClassFields = {
  type: ErrorTypes;
  code: number;
  message?: string;
};

export enum ErrorTypes {
  UNKNOWN = "UNKNOWN",
  OTC_FLOW_REQUEST = "OTC_FLOW_REQUEST",
  REGISTER_FLOW_REQUEST = "REGISTER_FLOW_REQUEST",
  LOGIN_FLOW_REQUEST = "LOGIN_FLOW_REQUEST",
  REGISTER_FLOW_USER_CONSENTS = "REGISTER_FLOW_USER_CONSENTS",
  REQUIRED_CONSENTS_REGISTER_FLOW = "REQUIRED_CONSENTS_REGISTER_FLOW",
  GET_CONSENTS = "GET_CONSENTS",
  POST_EXERCISE = "POST_EXERCISE",
  GET_EXERCISE = "GET_EXERCISE",
  PUT_EXERCISE = "PUT_EXERCISE",
  DELETE_EXERCISE = "DELETE_EXERCISE",
  POST_TRAINING = "POST_TRAINING",
  GET_TRAINING = "GET_TRAINING",
  PUT_TRAINING = "PUT_TRAINING",
  DELETE_TRAINING = "DELETE_TRAINING",
  POST_TRAINING_PLAN = "POST_TRAINING_PLAN",
  GET_TRAINING_PLAN = "GET_TRAINING_PLAN",
  PUT_TRAINING_PLAN = "PUT_TRAINING_PLAN",
  DELETE_TRAINING_PLAN = "DELETE_TRAINING_PLAN",
}
