import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class <PERSON>rrorOtcFlow extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.OTC_FLOW_REQUEST, error);
  }
}

export class <PERSON><PERSON>r<PERSON>egisterFlow extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.REGISTER_FLOW_REQUEST, error);
  }
}

export class <PERSON>rrorLoginFlow extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.LOGIN_FLOW_REQUEST, error);
  }
}

export class ErrorRegisterFlowUserConsents extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.REGISTER_FLOW_USER_CONSENTS, error);
  }
}
