import { EUserRole } from "@/enums/roles";
import { Consent } from "./consents";

export type OtcRequestPayload = {
  email: string;
  purpose: string;
};

export type RegisterRequestPayload = {
  email: string;
  name: string;
  password: string;
  consentIds: string[];
  authorization_code: string;
};

export type RegisterResponse = {
  id: string;
  email: string;
  name: string;
  consents: Consent[];
  roles: EUserRole[];
  createdAt: string;
};

export type LoginRequestPayload = {
  email: string;
  password: string;
};

export type LoginResponse = {
  accessToken: string;
  refreshToken: string;
  expiresIn: {
    accessToken: string;
    refreshToken: string;
  };
  user: User;
};

export type User = {
  id: string;
  email: string;
  roles: EUserRole[];
  name: string;
};

export type CredentialsStoreData = Omit<LoginResponse, "user">;

export type EmailOtcParams = {
  email: string;
  name: string;
  password: string;
  consentIds: string; // JSON string of array
};
