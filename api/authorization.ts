import {
  Error<PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON>rro<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ErrorRegisterFlow,
} from "@/errors/ErrorRegisterFlow";
import axiosInstance from "@/middleware/axiosInstance";
import {
  LoginRequestPayload,
  LoginResponse,
  OtcRequestPayload,
  RegisterRequestPayload,
  RegisterResponse,
} from "@/types/authorization";

export const otcRequest = async (
  payload: OtcRequestPayload
): Promise<string> => {
  try {
    const response = await axiosInstance.post("/user/otc", payload);
    return response.data;
  } catch (error) {
    throw new ErrorOtcFlow(error);
  }
};

export const registerRequest = async (
  payload: RegisterRequestPayload
): Promise<RegisterResponse> => {
  try {
    const response = await axiosInstance.post("/user/create", payload);
    return response.data;
  } catch (error) {
    throw new ErrorRegisterFlow(error);
  }
};

export const loginRequest = async (
  payload: LoginRequestPayload
): Promise<LoginResponse> => {
  try {
    const response = await axiosInstance.post("/user/login", payload);
    return response.data;
  } catch (error) {
    throw new ErrorLoginFlow(error);
  }
};
